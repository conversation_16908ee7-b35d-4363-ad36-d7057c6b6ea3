import 'dart:io';

// 照片数据模型
class PhotoModel {
  final String id;
  final String filePath;
  final String fileName;
  final DateTime createdAt;
  final int fileSize;
  final int width;
  final int height;
  final List<String> watermarkIds;
  bool isSelected;

  PhotoModel({
    required this.id,
    required this.filePath,
    required this.fileName,
    required this.createdAt,
    required this.fileSize,
    required this.width,
    required this.height,
    this.watermarkIds = const [],
    this.isSelected = false,
  });

  // 获取文件对象
  File get file => File(filePath);

  // 检查文件是否存在
  bool get exists => file.existsSync();

  // 获取文件大小（格式化）
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  // 获取分辨率字符串
  String get resolution => '${width}x$height';

  // 复制方法
  PhotoModel copyWith({
    String? id,
    String? filePath,
    String? fileName,
    DateTime? createdAt,
    int? fileSize,
    int? width,
    int? height,
    List<String>? watermarkIds,
    bool? isSelected,
  }) {
    return PhotoModel(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      fileName: fileName ?? this.fileName,
      createdAt: createdAt ?? this.createdAt,
      fileSize: fileSize ?? this.fileSize,
      width: width ?? this.width,
      height: height ?? this.height,
      watermarkIds: watermarkIds ?? this.watermarkIds,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'filePath': filePath,
      'fileName': fileName,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'fileSize': fileSize,
      'width': width,
      'height': height,
      'watermarkIds': watermarkIds,
      'isSelected': isSelected,
    };
  }

  // 从Map创建
  factory PhotoModel.fromMap(Map<String, dynamic> map) {
    return PhotoModel(
      id: map['id'] ?? '',
      filePath: map['filePath'] ?? '',
      fileName: map['fileName'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      fileSize: map['fileSize']?.toInt() ?? 0,
      width: map['width']?.toInt() ?? 0,
      height: map['height']?.toInt() ?? 0,
      watermarkIds: List<String>.from(map['watermarkIds'] ?? []),
      isSelected: map['isSelected'] ?? false,
    );
  }

  @override
  String toString() {
    return 'PhotoModel(id: $id, fileName: $fileName, createdAt: $createdAt, fileSize: $formattedFileSize, resolution: $resolution)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoModel && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}

// 照片选择状态
enum PhotoSelectionMode {
  none, // 无选择模式
  single, // 单选模式
  multiple, // 多选模式
}
