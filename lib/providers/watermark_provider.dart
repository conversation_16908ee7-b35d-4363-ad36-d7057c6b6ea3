import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/watermark_model.dart';
import '../utils/constants.dart';

// 水印状态管理
class WatermarkProvider extends ChangeNotifier {
  final List<WatermarkModel> _availableWatermarks = [];
  final List<WatermarkModel> _activeWatermarks = [];
  WatermarkModel? _selectedWatermark;
  bool _isWatermarkSelectorVisible = false;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<WatermarkModel> get availableWatermarks =>
      List.unmodifiable(_availableWatermarks);
  List<WatermarkModel> get activeWatermarks =>
      List.unmodifiable(_activeWatermarks);
  WatermarkModel? get selectedWatermark => _selectedWatermark;
  bool get isWatermarkSelectorVisible => _isWatermarkSelectorVisible;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasActiveWatermarks => _activeWatermarks.isNotEmpty;

  // 初始化水印列表
  void initializeWatermarks() {
    _isLoading = true;
    notifyListeners();

    _availableWatermarks.clear();

    for (int i = 0; i < AppConstants.watermarkUrls.length; i++) {
      final url = AppConstants.watermarkUrls[i];
      final name = _extractNameFromUrl(url);

      _availableWatermarks.add(
        WatermarkModel(id: const Uuid().v4(), url: url, name: name),
      );
    }

    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }

  // 从URL提取名称
  String _extractNameFromUrl(String url) {
    final uri = Uri.parse(url);
    final fileName = uri.pathSegments.last;
    // 移除文件扩展名
    final nameWithoutExtension = fileName.split('.').first;
    return nameWithoutExtension;
  }

  // 设置错误信息
  void setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  // 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 显示/隐藏水印选择器
  void toggleWatermarkSelector() {
    _isWatermarkSelectorVisible = !_isWatermarkSelectorVisible;
    notifyListeners();
  }

  // 显示水印选择器
  void showWatermarkSelector() {
    _isWatermarkSelectorVisible = true;
    notifyListeners();
  }

  // 隐藏水印选择器
  void hideWatermarkSelector() {
    _isWatermarkSelectorVisible = false;
    notifyListeners();
  }

  // 选择水印
  void selectWatermark(WatermarkModel watermark, {Offset? initialPosition}) {
    // 取消之前选中的水印
    if (_selectedWatermark != null) {
      final index = _activeWatermarks.indexWhere(
        (w) => w.id == _selectedWatermark!.id,
      );
      if (index != -1) {
        _activeWatermarks[index] = _activeWatermarks[index].copyWith(
          isSelected: false,
        );
      }
    }

    // 检查是否已经添加过这个水印
    final existingIndex = _activeWatermarks.indexWhere(
      (w) => w.url == watermark.url,
    );

    if (existingIndex != -1) {
      // 如果已存在，选中它
      _activeWatermarks[existingIndex] = _activeWatermarks[existingIndex]
          .copyWith(isSelected: true);
      _selectedWatermark = _activeWatermarks[existingIndex];
    } else {
      // 如果不存在，添加新的水印
      final newWatermark = watermark.copyWith(
        id: const Uuid().v4(),
        isSelected: true,
        position: initialPosition ?? const Offset(0, 0), // 默认在几何中心
      );
      _activeWatermarks.add(newWatermark);
      _selectedWatermark = newWatermark;
    }

    notifyListeners();
  }

  // 取消选择水印
  void deselectWatermark() {
    if (_selectedWatermark != null) {
      final index = _activeWatermarks.indexWhere(
        (w) => w.id == _selectedWatermark!.id,
      );
      if (index != -1) {
        _activeWatermarks[index] = _activeWatermarks[index].copyWith(
          isSelected: false,
        );
      }
      _selectedWatermark = null;
      notifyListeners();
    }
  }

  // 选择活动水印
  void selectActiveWatermark(String watermarkId) {
    // 取消之前的选择
    deselectWatermark();

    // 选择新的水印
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      _activeWatermarks[index] = _activeWatermarks[index].copyWith(
        isSelected: true,
      );
      _selectedWatermark = _activeWatermarks[index];
      notifyListeners();
    }
  }

  // 更新水印位置
  void updateWatermarkPosition(String watermarkId, Offset position) {
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      _activeWatermarks[index] = _activeWatermarks[index].copyWith(
        position: position,
      );

      // 如果是当前选中的水印，也更新选中状态
      if (_selectedWatermark?.id == watermarkId) {
        _selectedWatermark = _activeWatermarks[index];
      }

      notifyListeners();
    }
  }

  // 更新水印缩放
  void updateWatermarkScale(String watermarkId, double scale) {
    final clampedScale = scale.clamp(
      AppConstants.minWatermarkScale,
      AppConstants.maxWatermarkScale,
    );
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      _activeWatermarks[index] = _activeWatermarks[index].copyWith(
        scale: clampedScale,
      );

      // 如果是当前选中的水印，也更新选中状态
      if (_selectedWatermark?.id == watermarkId) {
        _selectedWatermark = _activeWatermarks[index];
      }

      notifyListeners();
    }
  }

  // 更新水印旋转
  void updateWatermarkRotation(String watermarkId, double rotation) {
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      _activeWatermarks[index] = _activeWatermarks[index].copyWith(
        rotation: rotation,
      );

      // 如果是当前选中的水印，也更新选中状态
      if (_selectedWatermark?.id == watermarkId) {
        _selectedWatermark = _activeWatermarks[index];
      }

      notifyListeners();
    }
  }

  // 更新水印透明度
  void updateWatermarkOpacity(String watermarkId, double opacity) {
    final clampedOpacity = opacity.clamp(0.0, 1.0);
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      _activeWatermarks[index] = _activeWatermarks[index].copyWith(
        opacity: clampedOpacity,
      );

      // 如果是当前选中的水印，也更新选中状态
      if (_selectedWatermark?.id == watermarkId) {
        _selectedWatermark = _activeWatermarks[index];
      }

      notifyListeners();
    }
  }

  // 删除水印
  void removeWatermark(String watermarkId) {
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      _activeWatermarks.removeAt(index);

      // 如果删除的是当前选中的水印，清除选择
      if (_selectedWatermark?.id == watermarkId) {
        _selectedWatermark = null;
      }

      notifyListeners();
    }
  }

  // 清除所有水印
  void clearAllWatermarks() {
    _activeWatermarks.clear();
    _selectedWatermark = null;
    notifyListeners();
  }

  // 复制水印
  void duplicateWatermark(String watermarkId) {
    final index = _activeWatermarks.indexWhere((w) => w.id == watermarkId);
    if (index != -1) {
      final originalWatermark = _activeWatermarks[index];
      final duplicatedWatermark = originalWatermark.copyWith(
        id: const Uuid().v4(),
        position: Offset(
          originalWatermark.position.dx + 20,
          originalWatermark.position.dy + 20,
        ),
        isSelected: false,
      );
      _activeWatermarks.add(duplicatedWatermark);
      notifyListeners();
    }
  }

  // 重置所有水印到默认状态
  void resetAllWatermarks() {
    for (int i = 0; i < _activeWatermarks.length; i++) {
      _activeWatermarks[i] = _activeWatermarks[i].copyWith(
        position: const Offset(0, 0), // 重置到几何中心
        scale: AppConstants.defaultWatermarkScale,
        rotation: 0.0,
        opacity: AppConstants.defaultWatermarkOpacity,
        isSelected: false,
      );
    }
    _selectedWatermark = null;
    notifyListeners();
  }
}
