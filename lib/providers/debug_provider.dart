import 'package:flutter/foundation.dart';

/// Debug模式状态管理
/// 控制应用中调试UI元素的显示/隐藏
class DebugProvider extends ChangeNotifier {
  bool _isDebugMode = false;

  /// 获取当前Debug模式状态
  bool get isDebugMode => _isDebugMode;

  /// 切换Debug模式
  void toggleDebugMode() {
    _isDebugMode = !_isDebugMode;
    notifyListeners();
    
    // 输出状态变化日志（这个日志始终显示，用于确认功能正常）
    debugPrint('🐛 Debug模式已${_isDebugMode ? "开启" : "关闭"}');
  }

  /// 设置Debug模式状态
  void setDebugMode(bool enabled) {
    if (_isDebugMode != enabled) {
      _isDebugMode = enabled;
      notifyListeners();
      debugPrint('🐛 Debug模式已${_isDebugMode ? "开启" : "关闭"}');
    }
  }

  /// 条件化的debugPrint方法
  /// 只有在Debug模式开启时才输出日志
  void debugLog(String message) {
    if (_isDebugMode) {
      debugPrint(message);
    }
  }
}
