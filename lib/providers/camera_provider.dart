import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:camerawesome/camerawesome_plugin.dart';

// 相机状态管理
class CameraProvider extends ChangeNotifier {
  // 相机状态
  CameraState? _cameraState;
  bool _isInitialized = false;
  bool _isRecording = false;

  // 拍摄状态管理
  bool _isCapturing = false;
  Uint8List? _capturedFrame;

  // 相机设置
  Sensor _currentSensor = Sensor.position(SensorPosition.back);
  FlashMode _flashMode = FlashMode.auto;
  double _zoomLevel = 1.0; // 默认缩放倍率保持1.0
  double _exposureLevel = 0.0;
  bool _isFrontCameraMirrored = true;

  // 错误状态
  String? _errorMessage;

  // Getters
  CameraState? get cameraState => _cameraState;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  bool get isCapturing => _isCapturing;
  Uint8List? get capturedFrame => _capturedFrame;
  Sensor get currentSensor => _currentSensor;
  FlashMode get flashMode => _flashMode;
  double get zoomLevel => _zoomLevel;
  double get exposureLevel => _exposureLevel;
  bool get isFrontCameraMirrored => _isFrontCameraMirrored;
  String? get errorMessage => _errorMessage;

  // 是否为前置摄像头
  bool get isFrontCamera => _currentSensor.position == SensorPosition.front;

  // 是否为后置摄像头
  bool get isBackCamera => _currentSensor.position == SensorPosition.back;

  // 初始化相机
  void initializeCamera(CameraState state) {
    debugPrint('🎥 初始化相机状态');
    _cameraState = state;
    _isInitialized = true;
    _errorMessage = null;

    // 设置初始缩放级别为1.0
    _zoomLevel = 1.0;
    debugPrint('🔍 设置初始缩放级别: ${_zoomLevel.toStringAsFixed(2)}x');

    // 确保相机状态正确初始化后设置缩放
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        await Future.delayed(const Duration(milliseconds: 100));
        if (_cameraState != null) {
          debugPrint(
            '🔧 设置硬件初始缩放: 0.0 (对应UI缩放: ${_zoomLevel.toStringAsFixed(2)}x)',
          );
          await _cameraState!.sensorConfig.setZoom(0.0); // 0.0表示无缩放
          debugPrint('✅ 硬件初始缩放设置成功');
        }
      } catch (e) {
        debugPrint('❌ 初始化缩放设置失败: $e');
      }
    });

    notifyListeners();
    debugPrint('📢 相机初始化完成，通知UI更新');
  }

  // 设置错误信息
  void setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  // 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 切换前后摄像头
  Future<void> switchCamera() async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      // 切换传感器
      await _cameraState!.switchCameraSensor();

      // 更新当前传感器状态
      _currentSensor = _currentSensor.position == SensorPosition.back
          ? Sensor.position(SensorPosition.front)
          : Sensor.position(SensorPosition.back);

      // 重置缩放级别为1.0
      _zoomLevel = 1.0;

      // 等待一小段时间确保传感器切换完成
      await Future.delayed(const Duration(milliseconds: 200));

      // 应用新的缩放级别 (转换为0.0-1.0范围)
      try {
        final normalizedZoom = (_zoomLevel - 1.0) / 7.0; // 将1.0-8.0映射到0.0-1.0
        await _cameraState!.sensorConfig.setZoom(normalizedZoom);
      } catch (zoomError) {
        // 如果设置缩放失败，记录但不抛出错误
        debugPrint('设置缩放失败: $zoomError');
      }

      clearError(); // 清除之前的错误
      notifyListeners();
    } catch (e) {
      setError('切换摄像头失败: $e');
    }
  }

  // 设置闪光灯模式
  Future<void> setFlashMode(FlashMode mode) async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      // 检查是否为前置摄像头，前置摄像头通常不支持闪光灯
      if (_currentSensor.position == SensorPosition.front &&
          mode != FlashMode.none) {
        setError('前置摄像头不支持闪光灯');
        return;
      }

      await _cameraState!.sensorConfig.setFlashMode(mode);
      _flashMode = mode;
      clearError(); // 清除之前的错误
      notifyListeners();
    } catch (e) {
      setError('设置闪光灯失败: $e');
    }
  }

  // 切换闪光灯模式
  Future<void> toggleFlashMode() async {
    FlashMode newMode;
    switch (_flashMode) {
      case FlashMode.none:
        newMode = FlashMode.on;
        break;
      case FlashMode.on:
        newMode = FlashMode.auto;
        break;
      case FlashMode.auto:
        newMode = FlashMode.none;
        break;
      default:
        newMode = FlashMode.auto;
    }
    await setFlashMode(newMode);
  }

  // 设置缩放级别
  Future<void> setZoomLevel(double zoom) async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    // 限制缩放范围
    final clampedZoom = zoom.clamp(1.0, 8.0);

    // 如果缩放值没有实际变化，直接返回
    if ((_zoomLevel - clampedZoom).abs() < 0.001) {
      return;
    }

    // 记录缩放变化
    debugPrint(
      '🔍 缩放倍率更新: ${_zoomLevel.toStringAsFixed(2)}x -> ${clampedZoom.toStringAsFixed(2)}x',
    );

    // 立即更新UI状态，确保界面响应
    _zoomLevel = clampedZoom;
    notifyListeners();

    // 记录UI更新完成
    debugPrint('✅ UI缩放倍率已更新: ${_zoomLevel.toStringAsFixed(2)}x');

    // 异步处理硬件设置，不阻塞UI更新
    _setHardwareZoom(clampedZoom);
  }

  // 异步设置硬件缩放，不影响UI响应
  Future<void> _setHardwareZoom(double zoom) async {
    try {
      // 使用正确的缩放值范围 (0.0-1.0)
      final normalizedZoom = (zoom - 1.0) / 7.0; // 将1.0-8.0映射到0.0-1.0
      debugPrint(
        '🔧 设置硬件缩放: ${zoom.toStringAsFixed(2)}x (标准化值: ${normalizedZoom.toStringAsFixed(3)})',
      );

      await _cameraState!.sensorConfig.setZoom(normalizedZoom);
      debugPrint('✅ 硬件缩放设置成功: ${zoom.toStringAsFixed(2)}x');
      clearError(); // 清除之前的错误
    } catch (e) {
      debugPrint('❌ 设置硬件缩放失败: $e (缩放值: ${zoom.toStringAsFixed(2)}x)');
      // 硬件设置失败时不回滚UI状态，保持UI响应性
      // 只记录错误，不影响用户体验
      // setError('设置缩放失败: $e');
    }
  }

  // 设置曝光级别
  Future<void> setExposureLevel(double exposure) async {
    if (_cameraState == null) return;

    try {
      // 限制曝光范围
      final clampedExposure = exposure.clamp(-2.0, 2.0);
      await _cameraState!.sensorConfig.setBrightness(clampedExposure);
      _exposureLevel = clampedExposure;
      notifyListeners();
    } catch (e) {
      setError('设置曝光失败: $e');
    }
  }

  // 设置前置摄像头镜像
  Future<void> setFrontCameraMirrored(bool mirrored) async {
    if (_cameraState == null) return;

    try {
      // await _cameraState!.sensorConfig.setMirrorFrontCamera(mirrored); // API已更改
      _isFrontCameraMirrored = mirrored;
      notifyListeners();
    } catch (e) {
      setError('设置镜像失败: $e');
    }
  }

  // 拍照
  Future<void> takePhoto() async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      clearError(); // 清除之前的错误
      await _cameraState!.when(
        onPhotoMode: (photoState) async {
          debugPrint('开始拍照...');
          await photoState.takePhoto();
          debugPrint('拍照命令已发送');
        },
        onVideoMode: (videoState) async {
          // 如果在视频模式，直接抛出错误，因为当前配置为照片模式
          throw Exception('当前在视频模式，无法拍照');
        },
        onVideoRecordingMode: (videoRecordingState) {
          // 录制中不能拍照
          throw Exception('录制视频时无法拍照');
        },
        onPreparingCamera: (preparingState) {
          throw Exception('相机正在准备中，请稍后再试');
        },
      );
    } catch (e) {
      debugPrint('拍照失败: $e');
      setError('拍照失败: $e');
    }
  }

  // 开始录制视频
  Future<void> startVideoRecording() async {
    if (_cameraState == null || _isRecording) return;

    try {
      await _cameraState!.when(
        onVideoMode: (videoState) => videoState.startRecording(),
        onPhotoMode: (photoState) {
          // 如果在照片模式，先切换到视频模式
          // return photoState.switchToVideoMode(); // API已更改
          throw Exception('当前在照片模式，无法录制视频');
        },
        onVideoRecordingMode: (videoRecordingState) {
          // 已在录制中
          return;
        },
      );
      _isRecording = true;
      notifyListeners();
    } catch (e) {
      setError('开始录制失败: $e');
    }
  }

  // 停止录制视频
  Future<void> stopVideoRecording() async {
    if (_cameraState == null || !_isRecording) return;

    try {
      await _cameraState!.when(
        onVideoRecordingMode: (videoRecordingState) =>
            videoRecordingState.stopRecording(),
        onVideoMode: (videoState) {
          // 未在录制中
          return;
        },
        onPhotoMode: (photoState) {
          // 不在视频模式
          return;
        },
      );
      _isRecording = false;
      notifyListeners();
    } catch (e) {
      setError('停止录制失败: $e');
    }
  }

  // 开始拍摄（设置拍摄状态并保存截取的画面）
  void startCapturing(Uint8List? frameData) {
    _isCapturing = true;
    _capturedFrame = frameData;
    debugPrint('开始拍摄状态，截取画面: ${frameData?.length ?? 0} bytes');
    notifyListeners();
  }

  // 结束拍摄（清除拍摄状态和截取的画面）
  void endCapturing() {
    _isCapturing = false;
    _capturedFrame = null;
    debugPrint('结束拍摄状态');
    notifyListeners();
  }

  // 重置相机设置
  void resetSettings() {
    _zoomLevel = 1.0;
    _exposureLevel = 0.0;
    _flashMode = FlashMode.auto;
    _isFrontCameraMirrored = true;
    notifyListeners();
  }

  @override
  void dispose() {
    _cameraState = null;
    super.dispose();
  }
}
