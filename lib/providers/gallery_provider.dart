import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'dart:io';
import '../models/photo_model.dart';
import '../utils/permissions.dart';

// 相册状态管理
class GalleryProvider extends ChangeNotifier {
  final List<PhotoModel> _photos = [];
  final List<PhotoModel> _selectedPhotos = [];
  bool _isLoading = false;
  bool _isSelectionMode = false;
  String? _errorMessage;

  // 分页相关
  int _currentPage = 0;
  static const int _pageSize = 20;
  bool _hasMorePhotos = true;

  // Getters
  List<PhotoModel> get photos => List.unmodifiable(_photos);
  List<PhotoModel> get selectedPhotos => List.unmodifiable(_selectedPhotos);
  bool get isLoading => _isLoading;
  bool get isSelectionMode => _isSelectionMode;
  String? get errorMessage => _errorMessage;
  bool get hasMorePhotos => _hasMorePhotos;
  int get selectedCount => _selectedPhotos.length;
  bool get hasSelectedPhotos => _selectedPhotos.isNotEmpty;

  // 初始化相册
  Future<void> initializeGallery([BuildContext? context]) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      print('开始初始化相册...');

      // 首先测试所有权限状态
      final permissionStatus = await PermissionUtils.testAllPermissions();
      print('当前权限状态: $permissionStatus');

      // 使用更简单的权限申请方式
      final PermissionState ps = await PhotoManager.requestPermissionExtend();
      print('PhotoManager权限状态: ${ps.toString()}');

      // 如果权限被拒绝，尝试多种方式
      if (!ps.isAuth) {
        print('PhotoManager权限失败，尝试系统权限...');

        // 使用系统权限作为备选
        final hasPhotosPermission =
            await PermissionUtils.requestPhotosPermission();
        final hasStoragePermission =
            await PermissionUtils.requestStoragePermission();

        print('系统权限 - 相册: $hasPhotosPermission, 存储: $hasStoragePermission');

        if (!hasPhotosPermission && !hasStoragePermission) {
          if (context != null) {
            await PermissionUtils.showPhotosPermissionDialog(context);
          }
          throw Exception('相册权限被拒绝，请在设置中手动开启相册和存储权限');
        }
      }

      print('权限验证完成，开始加载照片...');

      // 清空现有数据
      _photos.clear();
      _currentPage = 0;
      _hasMorePhotos = true;

      // 加载第一页照片
      await _loadPhotos();
    } catch (e) {
      setError('初始化相册失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 加载照片
  Future<void> _loadPhotos() async {
    try {
      // 获取相册列表
      final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
        type: RequestType.image,
        onlyAll: true,
      );

      if (albums.isEmpty) {
        _hasMorePhotos = false;
        return;
      }

      // 获取所有照片的相册
      final AssetPathEntity allPhotosAlbum = albums.first;

      // 分页获取照片
      final List<AssetEntity> assets = await allPhotosAlbum.getAssetListPaged(
        page: _currentPage,
        size: _pageSize,
      );

      if (assets.isEmpty) {
        _hasMorePhotos = false;
        return;
      }

      // 筛选应用拍摄的照片
      final appPhotos = await _filterAppPhotos(assets);

      // 转换为PhotoModel
      for (final asset in appPhotos) {
        final file = await asset.file;
        if (file != null) {
          final photoModel = await _createPhotoModel(asset, file);
          _photos.add(photoModel);
        }
      }

      _currentPage++;

      // 如果返回的照片数量少于页面大小，说明没有更多照片了
      if (assets.length < _pageSize) {
        _hasMorePhotos = false;
      }
    } catch (e) {
      throw Exception('加载照片失败: $e');
    }
  }

  // 筛选应用拍摄的照片
  Future<List<AssetEntity>> _filterAppPhotos(List<AssetEntity> assets) async {
    final List<AssetEntity> appPhotos = [];

    try {
      for (final asset in assets) {
        final file = await asset.file;
        if (file != null) {
          final fileName = file.path.split('/').last;
          debugPrint('检查照片: $fileName, 路径: ${file.path}');

          // 主要依赖文件名模式匹配（系统相册中的照片路径与应用私有目录不同）
          if (fileName.startsWith('watermark_photo_')) {
            debugPrint('匹配到应用照片: $fileName');
            appPhotos.add(asset);
            continue;
          }

          // 检查其他可能的应用标识文件名
          if (fileName.startsWith('watermark_') ||
              fileName.contains('透卡相机') ||
              fileName.contains('watermark_camera') ||
              fileName.contains('watermark_composite_')) {
            debugPrint('匹配到应用照片（其他模式）: $fileName');
            appPhotos.add(asset);
            continue;
          }
        }
      }

      debugPrint('筛选出 ${appPhotos.length} 张应用照片，总共 ${assets.length} 张照片');
    } catch (e) {
      debugPrint('筛选应用照片失败: $e');
      // 如果筛选失败，返回空列表而不是所有照片，避免显示无关照片
      return [];
    }

    return appPhotos;
  }

  // 创建PhotoModel
  Future<PhotoModel> _createPhotoModel(AssetEntity asset, File file) async {
    final stat = await file.stat();

    return PhotoModel(
      id: asset.id,
      filePath: file.path,
      fileName: file.path.split('/').last,
      createdAt: asset.createDateTime,
      fileSize: stat.size,
      width: asset.width,
      height: asset.height,
    );
  }

  // 加载更多照片
  Future<void> loadMorePhotos() async {
    if (_isLoading || !_hasMorePhotos) return;

    _isLoading = true;
    notifyListeners();

    try {
      await _loadPhotos();
    } catch (e) {
      setError('加载更多照片失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 刷新相册
  Future<void> refreshGallery() async {
    _photos.clear();
    _selectedPhotos.clear();
    _currentPage = 0;
    _hasMorePhotos = true;
    _isSelectionMode = false;
    await initializeGallery();
  }

  // 设置错误信息
  void setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  // 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 进入选择模式
  void enterSelectionMode() {
    _isSelectionMode = true;
    notifyListeners();
  }

  // 退出选择模式
  void exitSelectionMode() {
    _isSelectionMode = false;
    _selectedPhotos.clear();
    // 更新所有照片的选择状态
    for (int i = 0; i < _photos.length; i++) {
      _photos[i] = _photos[i].copyWith(isSelected: false);
    }
    notifyListeners();
  }

  // 选择/取消选择照片
  void togglePhotoSelection(String photoId) {
    final index = _photos.indexWhere((photo) => photo.id == photoId);
    if (index != -1) {
      final photo = _photos[index];
      final newSelectionState = !photo.isSelected;

      _photos[index] = photo.copyWith(isSelected: newSelectionState);

      if (newSelectionState) {
        _selectedPhotos.add(_photos[index]);
      } else {
        _selectedPhotos.removeWhere((p) => p.id == photoId);
      }

      notifyListeners();
    }
  }

  // 全选照片
  void selectAllPhotos() {
    _selectedPhotos.clear();
    for (int i = 0; i < _photos.length; i++) {
      _photos[i] = _photos[i].copyWith(isSelected: true);
      _selectedPhotos.add(_photos[i]);
    }
    notifyListeners();
  }

  // 取消全选
  void deselectAllPhotos() {
    _selectedPhotos.clear();
    for (int i = 0; i < _photos.length; i++) {
      _photos[i] = _photos[i].copyWith(isSelected: false);
    }
    notifyListeners();
  }

  // 删除选中的照片
  Future<void> deleteSelectedPhotos() async {
    if (_selectedPhotos.isEmpty) return;

    _isLoading = true;
    notifyListeners();

    try {
      final List<String> failedDeletions = [];

      for (final photo in _selectedPhotos) {
        try {
          final file = File(photo.filePath);
          if (await file.exists()) {
            await file.delete();
          }

          // 从照片列表中移除
          _photos.removeWhere((p) => p.id == photo.id);
        } catch (e) {
          failedDeletions.add(photo.fileName);
        }
      }

      _selectedPhotos.clear();
      _isSelectionMode = false;

      if (failedDeletions.isNotEmpty) {
        setError('部分照片删除失败: ${failedDeletions.join(', ')}');
      }
    } catch (e) {
      setError('删除照片失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 添加新照片
  void addPhoto(PhotoModel photo) {
    _photos.insert(0, photo); // 插入到列表开头
    notifyListeners();
  }

  // 获取照片总数
  int get totalPhotosCount => _photos.length;
}
