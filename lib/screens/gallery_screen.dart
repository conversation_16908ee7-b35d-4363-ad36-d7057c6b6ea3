import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/gallery_provider.dart';
// import '../utils/constants.dart'; // 暂时不使用

// 相册界面
class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  @override
  void initState() {
    super.initState();
    // 刷新相册
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<GalleryProvider>().refreshGallery();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('我的照片', style: TextStyle(color: Colors.white)),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          Consumer<GalleryProvider>(
            builder: (context, galleryProvider, child) {
              if (galleryProvider.isSelectionMode) {
                return Row(
                  children: [
                    // 全选/取消全选
                    TextButton(
                      onPressed: () {
                        if (galleryProvider.selectedCount ==
                            galleryProvider.totalPhotosCount) {
                          galleryProvider.deselectAllPhotos();
                        } else {
                          galleryProvider.selectAllPhotos();
                        }
                      },
                      child: Text(
                        galleryProvider.selectedCount ==
                                galleryProvider.totalPhotosCount
                            ? '取消全选'
                            : '全选',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),

                    // 删除按钮
                    IconButton(
                      onPressed: galleryProvider.hasSelectedPhotos
                          ? () => _showDeleteDialog()
                          : null,
                      icon: Icon(
                        Icons.delete,
                        color: galleryProvider.hasSelectedPhotos
                            ? Colors.red
                            : Colors.grey,
                      ),
                    ),
                  ],
                );
              } else {
                return IconButton(
                  onPressed: () {
                    galleryProvider.enterSelectionMode();
                  },
                  icon: const Icon(Icons.select_all, color: Colors.white),
                );
              }
            },
          ),
        ],
      ),
      body: Consumer<GalleryProvider>(
        builder: (context, galleryProvider, child) {
          if (galleryProvider.isLoading && galleryProvider.photos.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(color: Colors.white),
            );
          }

          if (galleryProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  Text(
                    galleryProvider.errorMessage!,
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      galleryProvider.refreshGallery();
                    },
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          if (galleryProvider.photos.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.photo_library, color: Colors.grey, size: 64),
                  SizedBox(height: 16),
                  Text(
                    '还没有照片\n去拍摄一些精彩的照片吧！',
                    style: TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return GridView.builder(
            padding: const EdgeInsets.all(8),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 4,
              mainAxisSpacing: 4,
            ),
            itemCount: galleryProvider.photos.length,
            itemBuilder: (context, index) {
              final photo = galleryProvider.photos[index];
              return GestureDetector(
                onTap: () {
                  if (galleryProvider.isSelectionMode) {
                    galleryProvider.togglePhotoSelection(photo.id);
                  } else {
                    // 预览照片
                    _showPhotoPreview(photo);
                  }
                },
                onLongPress: () {
                  if (!galleryProvider.isSelectionMode) {
                    galleryProvider.enterSelectionMode();
                    galleryProvider.togglePhotoSelection(photo.id);
                  }
                },
                child: Stack(
                  children: [
                    // 照片缩略图
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: photo.isSelected
                            ? Border.all(color: Colors.blue, width: 3)
                            : null,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          File(photo.filePath),
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[800],
                              child: const Icon(
                                Icons.broken_image,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    // 选中状态指示器
                    if (galleryProvider.isSelectionMode)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: photo.isSelected
                                ? Colors.blue
                                : Colors.black54,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                          child: photo.isSelected
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : null,
                        ),
                      ),
                  ],
                ),
              );
            },
          );
        },
      ),
      // 底部状态栏
      bottomNavigationBar: Consumer<GalleryProvider>(
        builder: (context, galleryProvider, child) {
          if (galleryProvider.isSelectionMode &&
              galleryProvider.hasSelectedPhotos) {
            return Container(
              padding: const EdgeInsets.all(16),
              color: Colors.black87,
              child: Text(
                '已选择 ${galleryProvider.selectedCount} 张照片',
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  // 显示照片预览
  void _showPhotoPreview(photo) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 照片
            Image.file(File(photo.filePath), fit: BoxFit.contain),

            // 信息
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    photo.fileName,
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${photo.getFormattedFileSize()} • ${photo.getResolutionText()}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示删除确认对话框
  void _showDeleteDialog() {
    final galleryProvider = context.read<GalleryProvider>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除照片'),
        content: Text(
          '确定要删除选中的 ${galleryProvider.selectedCount} 张照片吗？此操作无法撤销。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              galleryProvider.deleteSelectedPhotos();
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
