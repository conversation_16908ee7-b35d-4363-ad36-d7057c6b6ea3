import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/watermark_model.dart';
import '../providers/watermark_provider.dart';
import '../providers/debug_provider.dart';
import '../utils/constants.dart';

// 可拖拽水印组件
class DraggableWatermark extends StatefulWidget {
  final WatermarkModel watermark;
  final Size containerSize;
  final Size actualPreviewSize;
  final Offset previewOffset;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  const DraggableWatermark({
    super.key,
    required this.watermark,
    required this.containerSize,
    required this.actualPreviewSize,
    required this.previewOffset,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  @override
  State<DraggableWatermark> createState() => _DraggableWatermarkState();
}

class _DraggableWatermarkState extends State<DraggableWatermark> {
  late Offset _relativePosition; // 相对于几何中心的偏移量
  late double _scale;
  late double _rotation;
  double _baseRotation = 0.0; // 记录旋转开始时的基础角度
  double _baseScale = 1.0; // 记录缩放开始时的基础缩放值
  Size? _actualDisplaySize; // 实际显示尺寸

  @override
  void initState() {
    super.initState();
    debugPrint('=== DraggableWatermark 初始化 ===');
    debugPrint('相对位置偏移: ${widget.watermark.position}');
    debugPrint('预览偏移: ${widget.previewOffset}');
    debugPrint('预览尺寸: ${widget.actualPreviewSize}');

    // position现在表示相对于几何中心的偏移量
    _relativePosition = widget.watermark.position;
    _scale = widget.watermark.scale;
    _rotation = _normalizeRotation(widget.watermark.rotation);

    debugPrint('初始化相对位置: $_relativePosition');
  }

  // 获取预览区域的几何中心位置
  Offset _getGeometricCenter() {
    final previewLeft = widget.previewOffset.dx;
    final previewTop = widget.previewOffset.dy;
    final centerX = previewLeft + widget.actualPreviewSize.width / 2;
    final centerY = previewTop + widget.actualPreviewSize.height / 2;

    return Offset(centerX, centerY);
  }

  // 将相对位置转换为绝对位置
  Offset _relativeToAbsolute(Offset relativePos) {
    final geometricCenter = _getGeometricCenter();
    return Offset(
      geometricCenter.dx + relativePos.dx,
      geometricCenter.dy + relativePos.dy,
    );
  }

  // 将绝对位置转换为相对位置
  Offset _absoluteToRelative(Offset absolutePos) {
    final geometricCenter = _getGeometricCenter();
    return Offset(
      absolutePos.dx - geometricCenter.dx,
      absolutePos.dy - geometricCenter.dy,
    );
  }

  // 标准化旋转值到-π到π之间
  double _normalizeRotation(double rotation) {
    double normalized = rotation % (2 * 3.14159);
    if (normalized > 3.14159) normalized -= 2 * 3.14159;
    if (normalized < -3.14159) normalized += 2 * 3.14159;
    return normalized;
  }

  @override
  void didUpdateWidget(DraggableWatermark oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.watermark != widget.watermark) {
      _relativePosition = widget.watermark.position;
      _scale = widget.watermark.scale;
      _rotation = _normalizeRotation(widget.watermark.rotation);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 计算几何中心位置
    final geometricCenter = _getGeometricCenter();

    // 计算实际位置（几何中心 + 相对偏移）
    final actualPosition = Offset(
      geometricCenter.dx + _relativePosition.dx,
      geometricCenter.dy + _relativePosition.dy,
    );

    // 计算左上角位置（从实际位置计算）
    final displaySize = _getDisplaySize();
    final leftTopPosition = Offset(
      actualPosition.dx - displaySize.width / 2,
      actualPosition.dy - displaySize.height / 2,
    );

    return Stack(
      children: [
        // 水印图片信息面板（紧贴紫色边框左上方）- Debug模式下显示
        Consumer<DebugProvider>(
          builder: (context, debugProvider, child) {
            if (!debugProvider.isDebugMode) return const SizedBox.shrink();
            return Positioned(
              left: leftTopPosition.dx,
              top: leftTopPosition.dy - 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                color: Colors.purple.withValues(alpha: 0.9),
                child: Text(
                  '相对坐标: (${_relativePosition.dx.toStringAsFixed(1)}, ${_relativePosition.dy.toStringAsFixed(1)}) | 缩放: ${_scale.toStringAsFixed(2)}x | 旋转: ${(_rotation * 180 / 3.14159).toStringAsFixed(1)}°',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          },
        ),
        // 水印主体
        Positioned(
          left: leftTopPosition.dx,
          top: leftTopPosition.dy,
          child: GestureDetector(
            onTap: () {
              widget.onTap?.call();
              // 选中当前水印
              context.read<WatermarkProvider>().selectActiveWatermark(
                widget.watermark.id,
              );
            },
            onDoubleTap: widget.onDoubleTap,
            onLongPress: () {
              // 长按显示删除确认对话框
              _showDeleteConfirmDialog(context);
            },
            onScaleStart: (details) {
              // 选中当前水印
              context.read<WatermarkProvider>().selectActiveWatermark(
                widget.watermark.id,
              );
              // 记录开始时的旋转角度和缩放值
              _baseRotation = _rotation;
              _baseScale = _scale;
            },
            onScaleUpdate: (details) {
              setState(() {
                // 简化操作逻辑：分离移动、缩放和旋转
                if (details.pointerCount == 1) {
                  // 单指拖拽：更新相对位置偏移
                  // 将拖拽增量直接应用到相对位置上，不限制移动范围
                  _relativePosition += details.focalPointDelta;
                } else if (details.pointerCount == 2) {
                  // 双指操作：处理缩放和旋转

                  // 缩放处理（基于初始缩放值）
                  final newScale = (_baseScale * details.scale).clamp(
                    AppConstants.minWatermarkScale,
                    AppConstants.maxWatermarkScale,
                  );
                  _scale = newScale;

                  // 旋转处理（基于初始角度，降低敏感度）
                  _rotation = _normalizeRotation(
                    _baseRotation + details.rotation * 0.5,
                  );
                }
              });

              // 更新Provider中的变换（保存相对位置）
              context.read<WatermarkProvider>().updateWatermarkPosition(
                widget.watermark.id,
                _relativePosition,
              );
              context.read<WatermarkProvider>().updateWatermarkScale(
                widget.watermark.id,
                _scale,
              );
              context.read<WatermarkProvider>().updateWatermarkRotation(
                widget.watermark.id,
                _rotation,
              );
            },
            onScaleEnd: (details) {
              // 缩放结束
            },
            child: Transform.scale(
              scale: _scale,
              child: Transform.rotate(
                angle: _rotation,
                child: Consumer<DebugProvider>(
                  builder: (context, debugProvider, child) {
                    return Container(
                      width: displaySize.width,
                      height: displaySize.height,
                      decoration: debugProvider.isDebugMode
                          ? BoxDecoration(
                              border: Border.all(
                                color: Colors.purple,
                                width: 2.0,
                              ),
                            )
                          : null,
                      child: Stack(
                        children: [
                          // 水印图像（设为不透明）
                          Opacity(
                            opacity: 1.0, // 强制设为不透明
                            child: CachedNetworkImage(
                              imageUrl: widget.watermark.url,
                              fit: BoxFit.contain,
                              imageBuilder: (context, imageProvider) {
                                return FutureBuilder<Size>(
                                  future: _getImageSize(imageProvider),
                                  builder: (context, snapshot) {
                                    if (snapshot.hasData) {
                                      final imageSize = snapshot.data!;
                                      final displaySize = _calculateDisplaySize(
                                        imageSize,
                                      );

                                      // 更新实际显示尺寸
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                            _setActualDisplaySize(displaySize);
                                          });

                                      return Image(
                                        image: imageProvider,
                                        width: displaySize.width,
                                        height: displaySize.height,
                                        fit: BoxFit.contain,
                                      );
                                    }
                                    return Container(
                                      width: AppConstants.defaultWatermarkSize,
                                      height: AppConstants.defaultWatermarkSize,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Center(
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              placeholder: (context, url) => Container(
                                width: AppConstants.defaultWatermarkSize,
                                height: AppConstants.defaultWatermarkSize,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: AppConstants.defaultWatermarkSize,
                                height: AppConstants.defaultWatermarkSize,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(
                                  Icons.error,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ),

                          // 移除所有操作按钮，使用长按删除
                        ],
                      ), // Stack
                    );
                  },
                ), // Consumer<DebugProvider>
              ), // Transform.rotate
            ), // Transform.scale
          ), // GestureDetector
        ), // Positioned
      ], // Stack children
    ); // Stack (return语句结束)
  }

  // 获取图片的原始尺寸
  Future<Size> _getImageSize(ImageProvider imageProvider) async {
    final completer = Completer<Size>();
    final imageStream = imageProvider.resolve(const ImageConfiguration());

    late ImageStreamListener listener;
    listener = ImageStreamListener((ImageInfo info, bool synchronousCall) {
      final image = info.image;
      completer.complete(Size(image.width.toDouble(), image.height.toDouble()));
      imageStream.removeListener(listener);
    });

    imageStream.addListener(listener);
    return completer.future;
  }

  // 计算显示尺寸，按照新的逻辑实现智能缩放
  Size _calculateDisplaySize(Size imageSize) {
    final containerWidth = widget.containerSize.width;
    final containerHeight = widget.containerSize.height;

    // 默认按照水印图片的原始大小显示
    double displayWidth = imageSize.width;
    double displayHeight = imageSize.height;

    // 如果水印宽度大于app宽度，则调整为app宽度，高度等比缩放
    if (displayWidth > containerWidth) {
      final widthScale = containerWidth / displayWidth;
      displayWidth = containerWidth;
      displayHeight = displayHeight * widthScale;
    }

    // 如果调整后的高度超出显示区域，则以高度为准，宽度等比缩放
    if (displayHeight > containerHeight) {
      final heightScale = containerHeight / displayHeight;
      displayHeight = containerHeight;
      displayWidth = displayWidth * heightScale;
    }

    return Size(displayWidth, displayHeight);
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('删除水印'),
          content: const Text('确定要删除这个水印吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                // 删除水印
                context.read<WatermarkProvider>().removeWatermark(
                  widget.watermark.id,
                );
                widget.onLongPress?.call();
              },
              child: const Text('删除', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  // 获取当前显示尺寸（不包含缩放，因为Transform.scale会处理缩放）
  Size _getDisplaySize() {
    // 如果有实际显示尺寸，使用实际尺寸（不乘以_scale，避免双重缩放）
    if (_actualDisplaySize != null) {
      return _actualDisplaySize!;
    }

    // 否则使用默认尺寸（不乘以_scale，避免双重缩放）
    return const Size(
      AppConstants.defaultWatermarkSize,
      AppConstants.defaultWatermarkSize,
    );
  }

  // 设置实际显示尺寸
  void _setActualDisplaySize(Size size) {
    if (_actualDisplaySize != size) {
      setState(() {
        _actualDisplaySize = size;
      });
    }
  }
}
