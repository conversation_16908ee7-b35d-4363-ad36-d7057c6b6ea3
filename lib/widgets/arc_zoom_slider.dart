import 'package:flutter/material.dart';
import 'dart:math' as math;

class ArcZoomSlider extends StatefulWidget {
  final double value;
  final double minValue;
  final double maxValue;
  final ValueChanged<double> onChanged;
  final double size;
  final Color activeColor;
  final Color inactiveColor;

  const ArcZoomSlider({
    super.key,
    required this.value,
    required this.minValue,
    required this.maxValue,
    required this.onChanged,
    this.size = 120,
    this.activeColor = Colors.blue,
    this.inactiveColor = Colors.grey,
  });

  @override
  State<ArcZoomSlider> createState() => _ArcZoomSliderState();
}

class _ArcZoomSliderState extends State<ArcZoomSlider> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景弧形
          CustomPaint(
            size: Size(widget.size, widget.size),
            painter: <PERSON><PERSON><PERSON><PERSON>(
              progress: 1.0,
              color: widget.inactiveColor.withValues(alpha: 0.3),
              strokeWidth: 8,
            ),
          ),
          // 进度弧形
          CustomPaint(
            size: Size(widget.size, widget.size),
            painter: Arc<PERSON>ain<PERSON>(
              progress:
                  (widget.value - widget.minValue) /
                  (widget.maxValue - widget.minValue),
              color: widget.activeColor,
              strokeWidth: 8,
            ),
          ),
          // 手势检测
          GestureDetector(
            onPanUpdate: (details) {
              final center = Offset(widget.size / 2, widget.size / 2);
              final position = details.localPosition - center;

              // 计算角度
              double angle = math.atan2(position.dy, position.dx);

              // 将角度转换为0-2π范围
              if (angle < 0) angle += 2 * math.pi;

              // 将角度映射到值范围（从右侧开始，顺时针）
              // 弧形从-π/4到5π/4（270度范围）
              const startAngle = -math.pi / 4;
              const endAngle = 5 * math.pi / 4;
              const totalAngle = endAngle - startAngle;

              // 调整角度到我们的范围
              double adjustedAngle = angle;
              if (adjustedAngle > 3 * math.pi / 2) {
                adjustedAngle -= 2 * math.pi;
              }

              // 限制在有效范围内
              adjustedAngle = adjustedAngle.clamp(startAngle, endAngle);

              // 计算进度
              final progress = (adjustedAngle - startAngle) / totalAngle;

              // 转换为值
              final newValue =
                  widget.minValue +
                  progress * (widget.maxValue - widget.minValue);

              widget.onChanged(
                newValue.clamp(widget.minValue, widget.maxValue),
              );
            },
            child: Container(
              width: widget.size,
              height: widget.size,
              color: Colors.transparent,
            ),
          ),
          // 中心显示值
          Container(
            width: widget.size * 0.6,
            height: widget.size * 0.6,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                widget.value.toStringAsFixed(1),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ArcPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  ArcPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // 弧形从-π/4到5π/4（270度）
    const startAngle = -math.pi / 4;
    const totalAngle = 3 * math.pi / 2; // 270度
    final sweepAngle = totalAngle * progress;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! ArcPainter ||
        oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
