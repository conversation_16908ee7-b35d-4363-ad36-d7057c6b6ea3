import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/watermark_provider.dart';
import '../utils/constants.dart';

// 水印控制面板
class WatermarkControlPanel extends StatelessWidget {
  const WatermarkControlPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WatermarkProvider>(
      builder: (context, watermarkProvider, child) {
        final selectedWatermark = watermarkProvider.selectedWatermark;

        if (selectedWatermark == null) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                '水印调整',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // 透明度控制
              _buildSliderControl(
                context,
                label: '透明度',
                value: selectedWatermark.opacity,
                min: 0.0,
                max: 1.0,
                divisions: 20,
                onChanged: (value) {
                  watermarkProvider.updateWatermarkOpacity(
                    selectedWatermark.id,
                    value,
                  );
                },
                valueFormatter: (value) => '${(value * 100).round()}%',
              ),

              const SizedBox(height: 12),

              // 缩放控制
              _buildSliderControl(
                context,
                label: '大小',
                value: selectedWatermark.scale,
                min: AppConstants.minWatermarkScale,
                max: AppConstants.maxWatermarkScale,
                divisions: 20,
                onChanged: (value) {
                  watermarkProvider.updateWatermarkScale(
                    selectedWatermark.id,
                    value,
                  );
                },
                valueFormatter: (value) => '${(value * 100).round()}%',
              ),

              const SizedBox(height: 12),

              // 旋转控制
              _buildSliderControl(
                context,
                label: '旋转',
                value: selectedWatermark.rotation,
                min: -3.14159,
                max: 3.14159,
                divisions: 36,
                onChanged: (value) {
                  watermarkProvider.updateWatermarkRotation(
                    selectedWatermark.id,
                    value,
                  );
                },
                valueFormatter: (value) =>
                    '${(value * 180 / 3.14159).round()}°',
              ),

              const SizedBox(height: 16),

              // 操作按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // 复制按钮
                  _buildActionButton(
                    icon: Icons.copy,
                    label: '复制',
                    onPressed: () {
                      watermarkProvider.duplicateWatermark(
                        selectedWatermark.id,
                      );
                    },
                  ),

                  // 删除按钮
                  _buildActionButton(
                    icon: Icons.delete,
                    label: '删除',
                    color: Colors.red,
                    onPressed: () {
                      watermarkProvider.removeWatermark(selectedWatermark.id);
                    },
                  ),

                  // 重置按钮
                  _buildActionButton(
                    icon: Icons.refresh,
                    label: '重置',
                    onPressed: () {
                      // 重置当前水印到默认状态
                      watermarkProvider.updateWatermarkScale(
                        selectedWatermark.id,
                        AppConstants.defaultWatermarkScale,
                      );
                      watermarkProvider.updateWatermarkRotation(
                        selectedWatermark.id,
                        0.0,
                      );
                      watermarkProvider.updateWatermarkOpacity(
                        selectedWatermark.id,
                        AppConstants.defaultWatermarkOpacity,
                      );
                      watermarkProvider.updateWatermarkPosition(
                        selectedWatermark.id,
                        const Offset(0, 0), // 重置到几何中心
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建滑块控制组件
  Widget _buildSliderControl(
    BuildContext context, {
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
    required String Function(double) valueFormatter,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            Text(
              valueFormatter(value),
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.primary,
            inactiveTrackColor: Colors.grey[600],
            thumbColor: AppColors.primary,
            overlayColor: AppColors.primary.withValues(alpha: 0.2),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  // 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color ?? AppColors.primary,
            borderRadius: BorderRadius.circular(24),
          ),
          child: IconButton(
            icon: Icon(icon, color: Colors.white, size: 24),
            onPressed: onPressed,
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }
}
