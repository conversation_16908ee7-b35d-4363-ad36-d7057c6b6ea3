import 'package:flutter/material.dart';

class VerticalZoomSlider extends StatefulWidget {
  final double value;
  final double minValue;
  final double maxValue;
  final ValueChanged<double> onChanged;
  final double height;
  final double width;
  final Color activeColor;
  final Color inactiveColor;
  final Color thumbColor;

  const VerticalZoomSlider({
    super.key,
    required this.value,
    required this.minValue,
    required this.maxValue,
    required this.onChanged,
    this.height = 200,
    this.width = 40,
    this.activeColor = Colors.blue,
    this.inactiveColor = Colors.grey,
    this.thumbColor = Colors.white,
  });

  @override
  State<VerticalZoomSlider> createState() => _VerticalZoomSliderState();
}

class _VerticalZoomSliderState extends State<VerticalZoomSlider> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(widget.width / 2),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景轨道
          Container(
            width: 4,
            height: widget.height - 20,
            decoration: BoxDecoration(
              color: widget.inactiveColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 进度轨道
          Positioned(
            bottom: 10,
            child: Container(
              width: 4,
              height: _getProgressHeight(),
              decoration: BoxDecoration(
                color: widget.activeColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          
          // 滑块手柄
          Positioned(
            bottom: _getThumbPosition(),
            child: GestureDetector(
              onPanUpdate: (details) {
                _handlePanUpdate(details);
              },
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: widget.thumbColor,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: widget.activeColor,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: widget.activeColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
            ),
          ),
          
          // 缩放值显示
          Positioned(
            top: 5,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${widget.value.toStringAsFixed(1)}x',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getProgressHeight() {
    final progress = (widget.value - widget.minValue) / (widget.maxValue - widget.minValue);
    return (widget.height - 20) * progress;
  }

  double _getThumbPosition() {
    final progress = (widget.value - widget.minValue) / (widget.maxValue - widget.minValue);
    return 10 + (widget.height - 40) * progress;
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    
    // 计算相对于滑块轨道的位置（从底部开始）
    final trackHeight = widget.height - 40; // 减去上下边距
    final relativeY = widget.height - localPosition.dy - 20; // 从底部计算
    
    // 限制在有效范围内
    final clampedY = relativeY.clamp(0.0, trackHeight);
    
    // 计算进度
    final progress = clampedY / trackHeight;
    
    // 转换为值
    final newValue = widget.minValue + progress * (widget.maxValue - widget.minValue);
    
    widget.onChanged(newValue.clamp(widget.minValue, widget.maxValue));
  }
}
